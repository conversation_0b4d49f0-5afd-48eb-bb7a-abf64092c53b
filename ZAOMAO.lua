-- 盗版死全家
if UnitClass("player")=="圣骑士"  then
local ZMPaladin = CreateFrame("Frame")
 eat4 = "特效治疗石"
 PP = "player"
 TG = "target"
 ZHP = "智慧审判"
 ZHS = "智慧圣印"
 ZYS = "正义圣印"
 SZS = "十字军圣印"
 ZB = nil
 ZDJX = 0
 ZDSL = 0
 ZDWD = 0
 ZDCY = 0
 ZDSP = 0
 ZNFX = 0
 ZDZF = 0
 D1 = 0
 qjs = 0
 CJ13 = 1
 chkg=1
 zlkg=0
 qxsoi=0
zczcoi=1
fnzcoi=1
zdkspoi=1
autoswoi=1
jyms=0
gcd=0
zyzs=1
shua=1200
kfour=1
if not fx then fx=0 end
 if not zhsy then zhsy=0 end
  if not zysy then zysy=0 end
  if not sp then sp=0 end
  if not sszd then sszd=0 end
  if not szjdj then szjdj=0 end
  
if not bltime then bltime = 0 end
if not jzndj then jzndj=0 end
if not stss then stss=1 end
startime = 0
function TryUseTrinkets()
	UseInventoryItem(13)
	UseInventoryItem(14)
    local  duration13 = GetInventoryItemCooldown("player", 13)
	local  duration14 = GetInventoryItemCooldown("player", 14)
	if duration13~=0 then 
	print("饰品1 正在CD中")
	end
	if duration14~=0 then 
	print("饰品2 正在CD中")
	end
end
local function FX()
    if Myma > 30 then
	   usesw("信仰圣契")
        CastSpellByName("奉献")
    else
	  usesw("信仰圣契")
        CastSpellByName("奉献(等级 1)")
    end
end

local function ZH()
    if not IsBuffActive(ZHS) and not IsBuffActive(ZHP, TG) then
	  usesw("希望圣物")
        CastSpellByName(ZHS)
    end
end

local function ZH2()
    if not IsBuffActive(SZS) and not IsBuffActive("十字军审判", TG) then
	 usesw("希望圣物")
        CastSpellByName(SZS)
    elseif not IsBuffActive("十字军审判", TG) and IsBuffActive(SZS, PP) and SpellReady("审判") then
        CastSpellByName("审判")
    end
end

local function ZH3()
    if not IsBuffActive("光明圣印") and not IsBuffActive("光明审判", TG) then
	   usesw("希望圣物")
        CastSpellByName("光明圣印")
    end
end

local function SD()
if SpellReady("神圣之盾") then
    if UnitMana(PP) > 1300 then
	   usesw("梦境守护圣契")
        CastSpellByName("神圣之盾")
    else
	    usesw("梦境守护圣契")
        CastSpellByName("神圣之盾(等级 1)")
    end
	end
end
function chkgoi()
if not chkg or chkg ==1 then  chkg = 0  print("自动忏悔  开") chButton0:SetText("忏悔") 
elseif chkg == 0 then chkg=1 print("自动忏悔  关") chButton0:SetText("不忏悔") end
end
local function shenp()
    if (IsBuffActive("命令圣印", PP) or IsBuffActive(ZYS, PP) or IsBuffActive(ZHS, PP) or IsBuffActive("光明圣印", PP)) and SpellReady("审判") then
        CastSpellByName("审判")
    end
end

local function shenpms()-- 2的时候不打
    if ZDSP == 0 then
        ZH()--智慧
    elseif ZDSP == 1 then
        ZH2() --十字军
    elseif ZDSP == 3 then
        ZH3()--光明
    end
end

--local function patime()
--if not st_timer then st_timer=0 end
--    if (st_timer < UnitAttackSpeed(PP) - 1.5 ) or UnitAttackSpeed(PP)< 1.75 then
 --       return true
--    else
 --       return false
 --   end
--end

local function patime()
if not st_timer then st_timer=0 end
    if (st_timer > 0.5 and UnitAttackSpeed(PP)-st_timer > 0.2  ) or UnitAttackSpeed(PP)< 1.7 then
        return true
    else
        return false
    end
end

gjwz = gjwz or nil
local function gettar()
if UnitHealth("target")==0 then TargetNearestEnemy() end
if not PlayerFrame.inCombat then CastSpellByName("攻击")end
end

--/script print((IsAttackAction(11)))
-------------------------------------------------------------------------------------------

local function ExecuteScript()

if not zddl  then print("The activation code is invalid.")end
if not result then ProcessName() end
if result~= zddl and dofiles==177.5 then print("error") else if dofiles~=0 then  Processsncode() end end
if dofiles ==0 then
--print(qjs)
if D1 == 1 or D1 == 2 or D1 == 3 or D1 == 4 or D1 == 5 or D1 == 6 or D1 == -1 then
gettar()
end

if  kfour==0 then 
--if SpellReady("自由之手")  then 
for i = 1, GetNumRaidMembers() do local unit = "raid"..i; if  IsBuffActive("法力涌动", unit) or (IsBuffActive("国王的诅咒", unit) and IsBuffActive("黑暗屈从", unit) )  then 
--SpellStopCasting()
TargetUnit(unit); CastSpellByName("自由之手") print("已解除队友受控.") break end 
end 
end
if  kfour==0 then 
for i = 1, GetNumRaidMembers() do local unit = "raid"..i; if UnitIsConnected(unit) and not UnitIsDeadOrGhost(unit) and IsBuffActive("沉睡麻痹", unit)  then 
TargetUnit(unit); CastSpellByName("清洁术") print("已解除队友受控.") print("团驱眼球")break end end end


     Myma = UnitMana(PP) / UnitManaMax(PP) * 100
    local Myhp = UnitHealth(PP) / UnitHealthMax(PP) * 100
    local h = UnitHealth(TG) / UnitHealthMax(TG) * 100

    if not CJ13 then CJ13 = 1 end
    if not startime then startime = 0 end

  
    if IsBuffActive("集中") then
			if UnitMana("player") >100 then
                if Myhp < 70  then
				TargetUnit("player")
                    CastSpellByName("圣光术")
					TargetLastTarget()
                elseif Myhp > 70 and Myhp < 90 then
				TargetUnit("player")
                    CastSpellByName("圣光闪现")
					TargetLastTarget()

                else
				TargetUnit("player")
                    CastSpellByName("圣光闪现(等级 1)")
					TargetLastTarget()
                end
				else
						TargetUnit("player")
                    CastSpellByName("圣光闪现(等级 1)")
					TargetLastTarget()
end
        end

	
	
	if ZDWD == 0 and not IsBuffActive("自律", PP) then
        if Myhp < 20 and Myhp > 0 then
			if SpellReady("圣盾术") then
            CastSpellByName("圣盾术")
			elseif SpellReady("圣佑术") then
			CastSpellByName("圣佑术")
			elseif not SpellReady("圣佑术") and SpellReady("保护之手") then
			TargetUnit("player")
			CastSpellByName("保护之手")
			TargetLastTarget()
				end
			end
	end

	
	
	

    if ZDJX == 0 and UnitLevel("player")>40 then
        if (IsBuffActive("圣盾术", PP) or IsBuffActive("保护之手", PP) or IsBuffActive("圣佑术", PP)) and Myhp < 80 and Myma > 30 then
		
            CastSpellByName("圣光术")
        elseif (IsBuffActive("圣盾术", PP) or IsBuffActive("保护之手", PP)or IsBuffActive("圣佑术", PP)) and Myhp < 80 and Myma < 30 then
            CastSpellByName("圣光闪现")
        end

	else
		 if (IsBuffActive("圣盾术", PP) or IsBuffActive("保护之手", PP) or IsBuffActive("圣佑术", PP)) and Myhp < 80 and Myma > 20 then
            CastSpellByName("圣光术")
        elseif (IsBuffActive("圣盾术", PP) or IsBuffActive("保护之手", PP)or IsBuffActive("圣佑术", PP)) and Myhp < 80 and Myma < 20 then
            CastSpellByName("圣光闪现")
		elseif (IsBuffActive("圣盾术", PP) or IsBuffActive("保护之手", PP)or IsBuffActive("圣佑术", PP)) and Myhp < 50 and Myma < 10 then
			 CastSpellByName("圣光术(等级 1)")
        end
    end

    if ZDSL == 0 and SpellReady("圣疗术") then
        if Myhp < 13 and Myhp > 0 and not IsBuffActive("圣盾术", PP) and not IsBuffActive("圣佑术", PP) and UnitAffectingCombat("player") then
            CastSpellByName("圣疗术")
            print("圣疗术")
        end
    end
	
if (D1 == 1 or D1 == 2 or D1 == 3 or D1 == 4 or D1 == 5 or D1 == 6 ) and stss==0 then 
		if GetItemCooldown("斯坦索姆圣水")==0 and UnitAffectingCombat("player") and  IsActionInRange(12)==1 and UnitCreatureType("target")=="亡灵" then
			UseItemByName("斯坦索姆圣水")
			CameraOrSelectOrMoveStart()
			CameraOrSelectOrMoveStop()
	end
end

    if ZDCY == 0 and not IsBuffActive("圣盾术") and not IsBuffActive("保护之手") and Myhp < 25 and dofiles==0then
        UseItemByName(eat4)
    end
	

-----------------------------------------------------------------------------------------------	


    if D1 == 0 then
        if SpellReady("圣光闪现") then
		usesw("光明圣契")
            Heart_HealMostWounded("standard")
        end
    end

    if SpellReady("清洁术") and dofiles==0 then
        if qjs == 2 then
            Dcr_Clean()
        elseif qjs == 0 then
            for i = 1, 32 do
                if UnitDebuff(PP, i, 1) then
                    CastSpellByName("清洁术", 1)
                    break
                end
            end
        end
    end
	
	 if SpellReady("自由之手")  and Myma>10 and zyzs==0 then
	 if IsBuffActive("刺耳怒吼", unit) or IsBuffActive("包围之网",PP) or  IsBuffActive("冰霜新星",PP) or  IsBuffActive("雷霆一击",PP) or  IsBuffActive("覆体之网",PP) or IsBuffActive("断筋",PP) or IsBuffActive("冲击波",PP) or IsBuffActive("蛛网",PP) or IsBuffActive("摔绊",PP) then
	 TargetUnit("player")
	 CastSpellByName("自由之手")
			TargetLastTarget()
	 end 
	 end

	 --/script map=GetSubZoneText() print(map)
	 
	
	
	if (D1 == 1 or D1 == 2 or D1 == 3  or D1 == 4 or D1 == 5 or D1==6 or D1 == 0) and zdkspoi==0 and UnitAffectingCombat("player")  then
		local  duration13 = GetInventoryItemCooldown("player", 13)
		local  duration14 = GetInventoryItemCooldown("player", 14)
		if duration13==0 then 
		UseInventoryItem(13)
		end
		if duration14==0 then 
		UseInventoryItem(14)
		end
    end
	

    if (D1 == 1 or D1 == 3 or D1 == 2 or D1 == 5 or D1==6 or D1==-1) and qxsoi==0 then
        local type = UnitCreatureType("target")
        if Myma > 30 and (type == "亡灵" or type == "恶魔") and SpellReady("驱邪术") then
            CastSpellByName("驱邪术")
        end
    end
	    if (D1 == 1 or D1 == 3 or D1 == 2 or D1 == 5 or D1==6 or D1==-1) then
	    if zczcoi==0 then
        if  SpellReady("制裁之锤") and  not IsBuffActive("旋风斩", "target") and not IsBuffActive("制裁之锤", "target") then
		  usesw("审判者圣契")
            CastSpellByName("制裁之锤")
        end
    end
	end
	 if (D1 == 1 or D1 == 3 or D1 == 2 or D1 == 5 or D1==6) then
	    if fnzcoi==0 then
        if  SpellReady("愤怒之锤") and h<20 then
            CastSpellByName("愤怒之锤")
        end
    end
	end

    if (D1 == 1 or D1 == 2) and IsBuffActive("拯救祝福", PP) then
        CancelBuff("拯救祝福")
		 CancelBuff("强效拯救祝福")
    end

    if (D1 == 1 or D1 == 2) and not IsBuffActive("正义之怒", PP) and not UnitIsDeadOrGhost(PP) then
        CastSpellByName("正义之怒")
    end

    if (D1 == 1 or D1 == 2) and ZDZF == 0 and SpellReady("正义圣印") then
        if (not IsBuffActive("庇护祝福", PP) and not IsBuffActive("强效庇护祝福", PP))  then
          	TargetUnit("player")
			CastSpellByName("庇护祝福")
			TargetLastTarget()
        end
    end
	
--/script print((AuraUtil.FindAuraByName("正义之怒", "player")))
--------------------------------------------------------
if D1 == 1 then 
	        shenpms()
		if Myma> 15 and IsBuffActive("正义圣印", PP) then
        shenp()
			elseif IsBuffActive("智慧圣印", PP) and not IsBuffActive(ZHP, TG) and ZDSP==0 then
		shenp()
			elseif IsBuffActive("十字军圣印", PP) and not IsBuffActive("十字军圣印", TG) and ZDSP==1 then
		shenp()	
			elseif IsBuffActive("光明圣印", PP) and not IsBuffActive("光明圣印", TG) and ZDSP==3 then
		shenp()	
		end
	end	
if D1 == 1 and SpellReady("正义圣印") then
    if IsBuffActive("圣盾术", PP) and Myhp >= 50 then
        CancelBuff("圣盾术")
        print("生命值大于50% 自动取消圣盾术..")
    end
	
        if SpellReady("神圣之盾") then
            SD()
        end

	if SpellReady("神圣打击") and not UnitIsFriend("player","target") and IsActionInRange(12)==1 then
      usesw("光辉圣契")
        CastSpellByName("神圣打击")

    end

    if  ZNFX == 0  and IsActionInRange(12)==1 and not SpellReady("神圣之盾") and SpellReady("奉献") then
        FX()
    end

        if not IsBuffActive(ZYS, PP) and not IsBuffActive(SZS, PP) and not IsBuffActive(ZHS, PP) and not IsBuffActive("光明圣印", PP) and (IsBuffActive(ZHP, TG) or IsBuffActive("十字军审判", TG) or IsBuffActive("光明审判", TG) or ZDSP==2)  then

			if Myma<20 then
			usesw("希望圣物")
			CastSpellByName(ZHS)
			else
		   usesw("希望圣物")
            CastSpellByName(ZYS)
			end
        end

	end

----------------------------------------------------------------------
if D1 == 2  and SpellReady("正义圣印") then

if GetRealZoneText()=="斯坦索姆"  and not SpellReady("圣疗术")  and not SpellReady("圣盾术") and not IsBuffActive("圣盾术","player")then
	if  UnitHealth("player")<700 and (GetSubZoneText()=="十字军广场" or  GetSubZoneText()=="血色十字军堡垒" or  GetSubZoneText()=="圣光大厅" or GetSubZoneText()=="物资库" or GetSubZoneText()=="赤色王座" )then

UseItemByName("堕落的灰烬使者")
end
end

    if  ZNFX == 0 and  SpellReady("奉献") and IsActionInRange(12)==1 then
        FX()
    end
shenpms()
	if  not IsBuffActive(ZYS, PP) and not IsBuffActive("十字军圣印", PP) and not IsBuffActive("智慧圣印", PP) and not IsBuffActive("光明圣印", PP) and (IsBuffActive(ZHP, TG) or IsBuffActive("十字军审判", TG) or IsBuffActive("光明审判", TG) or ZDSP==2 )  then
		if Myma <70 then
		  usesw("希望圣物")
            CastSpellByName(ZHS)
			else
			if not IsBuffActive("正义圣印", PP) then
			 usesw("希望圣物")
			CastSpellByName("正义圣印(等级 1)")
			end
			end
    end
	
	if UnitMana(PP) >= 153 and (IsBuffActive(ZYS, PP) or IsBuffActive("智慧圣印", PP) or IsBuffActive("十字军圣印", PP)) then
        if SpellReady("神圣之盾") then
            SD()
        end
    end

    if 	IsActionInRange(12)==1 and UnitMana(PP) >= 153 and (IsBuffActive(ZYS, PP) or IsBuffActive("智慧圣印", PP) or IsBuffActive("十字军圣印", PP)or IsBuffActive("光明圣印", PP)) and SpellReady("神圣打击") and not UnitIsFriend("player","target")then
		if Myma >15 then
        usesw("热情圣契")
        CastSpellByName("十字军打击")
		else
		 usesw("光辉圣契")
        CastSpellByName("神圣打击")

		end
    end



end
if D1==2  then
     
    if UnitHealth(TG) > shua then
	if IsBuffActive("正义圣印", PP) then
        shenp()
			elseif IsBuffActive("智慧圣印", PP) and not IsBuffActive(ZHP, TG) and ZDSP==0 then
		shenp()
			elseif IsBuffActive("十字军圣印", PP) and not IsBuffActive("十字军圣印", TG) and ZDSP==1 then
		shenp()	
			elseif IsBuffActive("光明圣印", PP) and not IsBuffActive("光明圣印", TG) and ZDSP==3 then
		shenp()	
		end
    end
 end
 
 
 
------------------------------------------------------------------------------------
--if D1==3 then
-- buffi=0 for buffj=1,40 do if UnitBuff("target",buffj) then buffi=buffi+1 end end 
-- if  buffi>27 then jyms=1 else jyms=0 end
-- end
 
if D1==3 and jyms==0 then
	shenpms()
   	if Myma> 15 and IsBuffActive("正义圣印", PP) then
        shenp()
			elseif IsBuffActive("智慧圣印", PP) and not IsBuffActive(ZHP, TG) and ZDSP==0 then
		shenp()
			elseif IsBuffActive("十字军圣印", PP) and not IsBuffActive("十字军圣印", TG) and ZDSP==1 then
		shenp()	
			elseif IsBuffActive("光明圣印", PP) and not IsBuffActive("光明圣印", TG) and ZDSP==3 then
		shenp()	
		end
  if ((not IsBuffActive("智慧圣印", PP) and not IsBuffActive("十字军圣印",PP) and not IsBuffActive("光明圣印",PP)) ) or ZDSP==2 then
		if not IsBuffActive("正义圣印", PP) and SpellReady("正义圣印") then CastSpellByName("正义圣印")   end
  end
	end

    if D1 == 3 and SpellReady("正义圣印") then
    if not IsBuffActive("圣洁光环") and not IsBuffActive("虔诚光环") and not IsBuffActive("惩罚光环") and not IsBuffActive("专注光环") and not IsBuffActive("暗影抗性光环") and not IsBuffActive("冰霜抗性光环")and not IsBuffActive("火焰抗性光环")  then
            CastSpellByName("圣洁光环")
     end
   
    if  SpellReady("神圣打击") and patime() and not UnitIsFriend("player","target")  and IsActionInRange(12)==1then
        if GetTime() - startime > 19.5  and CJ13==0 then
            startime = GetTime()
           usesw("光辉圣契")
            CastSpellByName("神圣打击")

        else
            usesw("热情圣契")
            CastSpellByName("十字军打击")
        end
    end

   if chkg==0 and not IsBuffActive("忏悔") and SpellReady("忏悔") then
     CastSpellByName("忏悔")
   end

    if  ZNFX == 0 and not SpellReady("神圣打击") and not IsBuffActive(ZHS, PP) and IsActionInRange(12)==1 and SpellReady("奉献") then
        FX()
    end
	end
	
	
if D1==3 and jyms==1  then

if chkg==0 and SpellReady("忏悔") then
     CastSpellByName("忏悔")
end
--/script print((CheckInteractDistance("target", 0)))
--/script print((CheckInteractDistance("target", 1)))
--/script print((CheckInteractDistance("target", 2)))
--/script print((CheckInteractDistance("target", 3)))
--/script print((CheckInteractDistance("target", 4)))
--/script print((CheckInteractDistance("target", 5)))
if SpellReady("审判")  and CheckInteractDistance("target", 2) then  CastSpellByName("审判")   gcd=GetTime()  end

if  not IsBuffActive("正义圣印", PP) and not SpellReady("审判")  then CastSpellByName("正义圣印")   end

if IsBuffActive("正义圣印", PP) then

if  SpellReady("神圣打击")  and patime() and not UnitIsFriend("player","target")  and IsActionInRange(12)==1 then
        if GetTime() - startime > 19.5  and CJ13==0 then
            startime = GetTime()
          usesw("光辉圣契")
            CastSpellByName("神圣打击")
        else
           usesw("热情圣契")
            CastSpellByName("十字军打击")
        end
end
	
if  UnitMana(PP) >= 153 and SpellReady("神圣之盾") then
            SD()
end	

 if  ZNFX == 0  and not SpellReady("神圣打击")  and IsActionInRange(12)==1 and SpellReady("奉献") then
        FX()
 end

end
 end
	
	
	
-------------------------------------------------------------------------------

   if D1 == 4  and SpellReady("正义圣印")then
   
	if IsActionInRange(12)==1 and not UnitIsFriend("player","target") and UnitExists("target")  then

	    if jzndj==0 and SpellReady("神圣打击") and UnitHealth("target")>0 then
            CastSpellByName("神圣打击")
			end
			
        if jzndj==1 and not SpellReady("神圣震击") and SpellReady("十字军打击") and UnitHealth("target")>0  then 
			usesw("热情圣契")
            CastSpellByName("十字军打击(等级 1)")
			end
		if jzndj==1 and SpellReady("神圣震击") and SpellReady("神圣打击") and UnitHealth("target")>0   then
			usesw("光辉圣契")
			 CastSpellByName("神圣打击")
        end
	if  ZNFX == 0  and not SpellReady("神圣打击") and SpellReady("奉献") then
		 usesw("信仰圣契")
        CastSpellByName("奉献(等级 1)")
    end
	if SpellReady("圣光闪现")  and (IsBuffActive("正义圣印", "player") or IsBuffActive("智慧圣印", "player")) and zlkg==0 and not IsBuffActive("堕落心灵","player") then
		usesw("光明圣契")
        Heart_HealMostWounded("standard")
        end
		
		shenpms()
		if Myma> 25 and IsBuffActive("正义圣印", "player") then
        shenp()
			elseif IsBuffActive("智慧圣印", "player") and not IsBuffActive(ZHP, "target") and ZDSP==0 then
		shenp()
			elseif IsBuffActive("十字军圣印", "player") and not IsBuffActive("十字军圣印", "target") and ZDSP==1 then
		shenp()	
			elseif IsBuffActive("光明圣印", "player") and not IsBuffActive("光明圣印", "target") and ZDSP==3 then
		shenp()	
		end
		
 		if not IsBuffActive(ZYS, "player") and not IsBuffActive(SZS, "player") and not IsBuffActive(ZHS, "player") and not IsBuffActive("光明圣印", "player") and (IsBuffActive(ZHP, "target") or IsBuffActive("十字军审判", "target") or IsBuffActive("光明审判", "target")) then
		if Myma<50 then
		usesw("希望圣物")
		CastSpellByName(ZHS)
		else
			if not IsBuffActive("正义圣印", "player") then
				CastSpellByName("正义圣印")
				end
		end
		end
		
		
	else
		if SpellReady("圣光闪现") and D1==4 and zlkg==0 and not IsBuffActive("堕落心灵", "player")then
		 usesw("光明圣契")
        Heart_HealMostWounded("standard")
        end
		end
    end

	
	
------------------------------------------------------------------------------------


if D1==5 then
  shenpms()
   	if Myma> 15 and IsBuffActive("正义圣印", PP) then
        shenp()
			elseif IsBuffActive("智慧圣印", PP) and not IsBuffActive(ZHP, TG) and ZDSP==0 then
		shenp()
			elseif IsBuffActive("十字军圣印", PP) and not IsBuffActive("十字军圣印", TG) and ZDSP==1 then
		shenp()	
			elseif IsBuffActive("光明圣印", PP) and not IsBuffActive("光明圣印", TG) and ZDSP==3 then
		shenp()	
			elseif IsBuffActive("命令圣印", PP)  then
		shenp()	
		end
		
     if ((not IsBuffActive("智慧圣印", PP) and not IsBuffActive("十字军圣印",PP) and not IsBuffActive("光明圣印",PP)) ) or ZDSP==2 then
			if not IsBuffActive("命令圣印", PP) and SpellReady("命令圣印") then CastSpellByName("命令圣印")  if not IsBuffActive("命令圣印", PP) and not IsBuffActive("正义圣印", PP) then  CastSpellByName(ZYS) end end
     end
end

    if D1 == 5 and SpellReady("正义圣印") then
    if not IsBuffActive("圣洁光环") and not IsBuffActive("虔诚光环") and not IsBuffActive("惩罚光环") and not IsBuffActive("专注光环") and not IsBuffActive("暗影抗性光环") and not IsBuffActive("冰霜抗性光环")and not IsBuffActive("火焰抗性光环")  then
            CastSpellByName("圣洁光环")
     end
    if  SpellReady("神圣打击") and patime()  and IsActionInRange(12)==1  then
        if GetTime() - startime > 19.5  and CJ13==0 then
            startime = GetTime()
          usesw("光辉圣契")
            CastSpellByName("神圣打击")

        else
          usesw("热情圣契")
            CastSpellByName("十字军打击")
        end
    end

   if  chkg==0 and not IsBuffActive("忏悔") and SpellReady("忏悔") then
     CastSpellByName("忏悔")
  end

    if  ZNFX == 0 and not SpellReady("神圣打击") and not IsBuffActive(ZHS, PP) and IsActionInRange(12)==1 and SpellReady("奉献") then
        FX()
    end
	end
	--------------------------------------------------------------------------------------
 if D1 == 6 and SpellReady("正义圣印") then

    if not IsBuffActive("圣洁光环") and not IsBuffActive("虔诚光环") and not IsBuffActive("惩罚光环") and not IsBuffActive("专注光环") and not IsBuffActive("暗影抗性光环") and not IsBuffActive("冰霜抗性光环")and not IsBuffActive("火焰抗性光环")  then
            CastSpellByName("圣洁光环")
     end

	if  chkg==0 and not IsBuffActive("忏悔") and SpellReady("忏悔") then
     CastSpellByName("忏悔")
	end
 
 if ZNFX == 0 and CheckInteractDistance("target", 2) and SpellReady("奉献") then
        FX()
    end

 if patime() and SpellReady("神圣打击") and not UnitIsFriend("player","target") and IsActionInRange(12)==1 and UnitHealth("target")>0 then
  usesw("光辉圣契")
     CastSpellByName("神圣打击")

 end
	  if  patime() then  shenp() end
      if not IsBuffActive("正义圣印")  then  CastSpellByName("正义圣印") end
	end


	
	-----------------------------------------------------------------------------------------------
if D1==-1 then
if not IsBuffActive("力量祝福", PP) and not IsBuffActive("智慧祝福", PP) and SpellReady("正义圣印") then
TargetUnit("player"); CastSpellByName("智慧祝福") CastSpellByName("力量祝福")
end

if Casting() then CastSpellByName("制裁之锤") end


	shenpms()
   	if  IsBuffActive("正义圣印", PP) then
        shenp()
			elseif IsBuffActive("智慧圣印", PP) and not IsBuffActive(ZHP, TG) and ZDSP==0 then
		shenp()
			elseif IsBuffActive("十字军圣印", PP) and not IsBuffActive("十字军圣印", TG) and ZDSP==1 then
		shenp()	
			elseif IsBuffActive("光明圣印", PP) and not IsBuffActive("光明圣印", TG) and ZDSP==3 then
		shenp()	
	end
		
	if ((not IsBuffActive("智慧圣印", PP) and not IsBuffActive("十字军圣印",PP) and not IsBuffActive("光明圣印",PP)) ) or ZDSP==2 then
		if not IsBuffActive("正义圣印", PP) and SpellReady("正义圣印") then CastSpellByName("正义圣印")   end
	end
 end

 if D1 == -1 and SpellReady("正义圣印") then
    if  SpellReady("神圣打击") and patime() and not UnitIsFriend("player","target")  and IsActionInRange(12)==1then
        if  CJ134==2 then
            CastSpellByName("神圣打击")
        else
            CastSpellByName("十字军打击")
        end
    end

    if  ZNFX == 0 and not SpellReady("神圣打击") and not IsBuffActive(ZHS, PP) and IsActionInRange(12)==1 and SpellReady("奉献") then
        FX()
    end

 end
	
    end
	end
--------------------------------------------------
ZAOMAODB = ZAOMAODB or {}

local button = CreateFrame("Button", "ZAOMAOButton", UIParent, "GameMenuButtonTemplate")
button:SetWidth(120)  
button:SetHeight(30)  
button:SetText(" ")  
button:SetPoint("CENTER", ZAOMAODB.x or 0, ZAOMAODB.y or 0)  
button:SetMovable(true)
button:EnableMouse(true)
button:RegisterForDrag("LeftButton")
button:SetScript("OnDragStart", function()
    button:StartMoving()  
end)
button:SetScript("OnDragStop", function()
    button:StopMovingOrSizing()  
    ZAOMAODB.x = button:GetLeft()
    ZAOMAODB.y = button:GetTop() - UIParent:GetHeight()

end)


local function ToggleZDSP()
    if (ZDSP == 0 or not ZDSP) then
        ZDSP = 1
    elseif ZDSP == 1 then
        ZDSP = 2
    elseif ZDSP == 2 then
        ZDSP = 3
    elseif ZDSP == 3 then
        ZDSP = 0
    end

 
    if ZDSP == 0 then
       
		if D1==1 or D1==2 or D1==4 or D1==3 or D1==5 or D1==-1 then 
		 print("A.保持审判-智慧")
		button:SetText("A.保持-智慧 ●")
		end
    elseif ZDSP == 1 then
	if D1==1 or D1==2 or D1==3 or D1==4 or D1==5 or D1==-1 then 
        print("B.保持审判-十字军")
		button:SetText("B.保持-十字 ●")
		end
    elseif ZDSP == 2 then
		if D1==1 or D1==2 or D1==3 or D1==4 or D1==5 or D1==-1 then 
        print("C.审判 关闭.")
		button:SetText("C.关闭 审判 ●")
		end
    elseif ZDSP == 3 then
	if D1==1 or D1==2 or D1==3 or D1==4 or D1==5 or D1==-1 then 
        print("D.保持审判-光明")
		button:SetText("D.保持-光明 ●")
		end
    end
end


button:SetScript("OnClick", function()
    ToggleZDSP()
end)

local znfxButton = CreateFrame("Button", "ZNFXButton", UIParent, "GameMenuButtonTemplate")
znfxButton:SetWidth(120)  
znfxButton:SetHeight(30)  
znfxButton:SetText(" ")  
znfxButton:SetPoint("TOP", button, "BOTTOM", 0, -5)  
znfxButton:SetScript("OnClick", function()
    if ZNFX == 0 and( D1==1 or D1==2 or D1==3 or D1==4 or D1==5 or D1==6 or D1==-1) then
        ZNFX = 1
        print("智能奉献 关闭")
		znfxButton:SetText("关闭 ●")
    elseif ZNFX == 1 and( D1==1 or D1==2 or D1==3 or D1==5 or D1==4 or D1==6 or D1==-1) then
        ZNFX = 0
        print("智能奉献 开启")
		znfxButton:SetText("开启 ●")
    elseif ZNFX == nil then
        print("还没有选择档位..")
    end
end)


local qjsButton = CreateFrame("Button", "QJSButton", UIParent, "GameMenuButtonTemplate")
qjsButton:SetWidth(120)  
qjsButton:SetHeight(30)  
qjsButton:SetText("自我驱散 ●")  
qjsButton:SetPoint("TOP", znfxButton, "BOTTOM", 0, -5)  
qjsButton:SetScript("OnClick", function()

    if not qjs then
        qjs = 0
    end
    if qjs < 2 then
        qjs = qjs + 1
    else
        qjs = 0
    end

    if qjs == 0 then
        print("自我清洁术模式")
		qjsButton:SetText("自我驱散 ●")
		modeButton6:SetText("自驱")  
    elseif qjs == 1 then
        print("关闭清洁术")
		qjsButton:SetText("不驱散 ●")
		modeButton6:SetText("不驱")  
    elseif qjs == 2 then
        print("一键驱除功能开启,跑跳中 优先驱散")
		qjsButton:SetText("团队驱散 ●")
		modeButton6:SetText("团驱")  
    end
end)


local cj13Button = CreateFrame("Button", "CJ13Button", UIParent, "GameMenuButtonTemplate")
cj13Button:SetWidth(120)  
cj13Button:SetHeight(30)  
cj13Button:SetText(" ")  
cj13Button:SetPoint("TOP", qjsButton, "BOTTOM", 0, -5)  

cj13Button:SetScript("OnClick", function()
if CJ134 == 1 and( D1==1 or D1==2 or D1==3 or D1==5 or D1==6) then
		if not CJ13 then
        CJ13 = 1
		end
	
		if CJ13 == 0 and (D1==5 or D1==3)then
        CJ13 = 1
		        print("惩戒 全 十打 ●")
		cj13Button:SetText("全 十打 ●")
		elseif CJ13 == 1 and(D1==5 or D1==3)then
        CJ13 = 0
		        print("惩戒 1:3")
		cj13Button:SetText("惩戒 1:3 ●")
		end
	
	elseif CJ134 == 0 and D1==4 then
		if not jzndj or jzndj==1 then jzndj=0 cj13Button:SetText("神打 治愈之光 ●")
		elseif jzndj==0 then jzndj=1 cj13Button:SetText("十打优先 ●")
		end
	elseif CJ134== 2 and D1==-1 then
		if not expdj or expdj==1 then expdj=0 cj13Button:SetText("全神打 ●")
		elseif expdj==0 then expdj=1 cj13Button:SetText("全十打 ●")
		end
end	
	
	
	
	
end)


local mosButton = CreateFrame("Button", "MOSButton", UIParent, "GameMenuButtonTemplate")
mosButton:SetWidth(120)  
mosButton:SetHeight(30)  
mosButton:SetText("请选模式") 
mosButton:SetPoint("TOP", cj13Button, "BOTTOM", 0, -5)  

local mos2Button = CreateFrame("Button", "MOS2Button", UIParent, "UIPanelButtonTemplate")
mos2Button:SetWidth(70) 
mos2Button:SetHeight(25) 
mos2Button:SetText("养生骑士") 
mos2Button:SetPoint("TOPRIGHT", mosButton, "BOTTOM", 10, -5)  

mos2Button:SetScript("OnClick", function()

print("养生骑士 v1.4.9")
print("调用方法 普通宏：/zmpal")
print("带 ● 的项目可以进行切换模式")
print("Bug反馈，更多工具 插件定制 wx：zaomao")
end)


-------------------------------------
local button1 = CreateFrame("Button", "ZAOMAOButton1", UIParent, "GameMenuButtonTemplate")
button1:SetWidth(40) 
button1:SetHeight(30) 
button1:SetText("审判")  
button1:SetPoint("LEFT", button, "BOTTOM", 60, 15)  

local znfx1Button = CreateFrame("Button", "ZNFX1Button", UIParent, "GameMenuButtonTemplate")
znfx1Button:SetWidth(40) 
znfx1Button:SetHeight(30)  
znfx1Button:SetText("奉献")  
znfx1Button:SetPoint("LEFT", znfxButton, "BOTTOM", 60, 15)  


local qjs1Button = CreateFrame("Button", "QJS1Button", UIParent, "GameMenuButtonTemplate")
qjs1Button:SetWidth(40)  
qjs1Button:SetHeight(30) 
qjs1Button:SetText("驱散")  
qjs1Button:SetPoint("LEFT", qjsButton, "BOTTOM", 60, 15)   

local cj131Button = CreateFrame("Button", "CJ131Button", UIParent, "GameMenuButtonTemplate")
cj131Button:SetWidth(40) 
cj131Button:SetHeight(30) 
cj131Button:SetText("打击") 
cj131Button:SetPoint("LEFT", cj13Button, "BOTTOM", 60, 15)   


local mos1Button = CreateFrame("Button", "MOS1Button", UIParent, "GameMenuButtonTemplate")
mos1Button:SetWidth(40) 
mos1Button:SetHeight(30) 
mos1Button:SetText("模式") 
mos1Button:SetPoint("LEFT", mosButton, "BOTTOM", 60, 15)   
------------------------------------------------------------------------

local setButton = CreateFrame("Button", "MOSButton", UIParent, "GameMenuButtonTemplate")
setButton:SetWidth(50)  
setButton:SetHeight(25)  
setButton:SetText("▼")  
setButton:SetPoint("TOPLEFT", mosButton, "BOTTOM", 10, -5)  

setButton:SetScript("OnClick", function()
    if button:IsShown() then
	setButton:SetText("▲") 
        button:Hide()
		znfxButton:Hide()
		qjsButton:Hide()
		cj13Button:Hide()
		mosButton:Hide()
		mos2Button:Hide()
		button1:Hide()
		znfx1Button:Hide()
		qjs1Button:Hide()
		cj131Button:Hide()
		mos1Button:Hide()
		ssButton6:Hide()
		yuliuButton6:Hide()
		swButton6:Hide()
		zyzsButton6:Hide()
		kfourButton6:Hide()
		qcButton6:Hide()
    else
	setButton:SetText("▼") 
		button:Show()
		znfxButton:Show()
		qjsButton:Show()
		cj13Button:Show()
		mosButton:Show()
		mos2Button:Show()
		button1:Show()
		znfx1Button:Show()
		qjs1Button:Show()
		cj131Button:Show()
		mos1Button:Show()
		ssButton6:Show()
		yuliuButton6:Show()
		swButton6:Show()
		zyzsButton6:Show()
		kfourButton6:Show()
		qcButton6:Show()
    end
end)

------------------------------------------------------------------------
function dw()
if D1==0 then mosButton:SetText("0.传统治疗") button:SetText("强制 无审判") znfxButton:SetText("无") qjsButton:SetText("自我驱散 ●") cj13Button:SetText("无 打击")
elseif D1==1 then mosButton:SetText("1.MT模式") button:SetText("B.保持-十字 ●") znfxButton:SetText("开启 ●") qjsButton:SetText("自我驱散 ●") cj13Button:SetText("强制神打")
elseif D1==2 then mosButton:SetText("2.单刷模式") button:SetText("A.保持-智慧 ●") znfxButton:SetText("开启 ●") qjsButton:SetText("自我驱散 ●") cj13Button:SetText("单刷智能打击")
elseif D1==3 then mosButton:SetText("3.惩戒混伤") button:SetText("A.保持-智慧 ●") znfxButton:SetText("开启 ●") qjsButton:SetText("不驱散 ●") cj13Button:SetText("全 十打 ●")
elseif D1==4 then mosButton:SetText("4.近奶 高级模式") button:SetText("A.保持-智慧 ●") znfxButton:SetText("关闭 ●")  qjsButton:SetText("自我驱散 ●") cj13Button:SetText("神打 治愈之光 ●")
elseif D1==5 then mosButton:SetText("5.惩戒物理") button:SetText("C.关闭 审判 ●") znfxButton:SetText("关闭 ●") qjsButton:SetText("不驱散 ●") cj13Button:SetText("全 十打 ●")
elseif D1==6 then mosButton:SetText("6.AOE模式") button:SetText("强制 正义审判") znfxButton:SetText("开启 ●") qjsButton:SetText("不驱散 ●") cj13Button:SetText("AOE模式神打")
elseif D1==-1 then mosButton:SetText("练级30模式") button:SetText("A.保持-智慧 ●") znfxButton:SetText("开启 ●") qjsButton:SetText("不驱散 ●") cj13Button:SetText("全神打 ●")
end
	if ZDWD==0 then yuliuButton6:SetText("自保开")
	else  yuliuButton6:SetText("自保关")
	end
end

local function ApplyMode()
    if D1 == 1 then
        ZB = 0
        LYZN = 1
        ZDWD = 1
        ZDCY = 0
        ZDZF = 0
        ZNFX = 0
        ZCZC = 1
        ZDJX = 1
        qjs = 0
        ZDSL = 0
        YJZL = 1
        ZDSP = 1
		chkg = 1
		zlkg=0
		CJ134=1
		dw()
		qjsshow()
	print((D1 .. "档 --MT模式"))
    elseif D1 == 2 then

        ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 0
        ZNFX = 0
        ZCZC = 0
        ZDJX = 0
        qjs = 0
        ZDSL = 0
        YJZL = 1
        ZDSP = 0
		chkg = 1
		zlkg=0
		CJ134=1
		dw()
		qjsshow()
        print((D1 .. "档 --单刷T模式"))
    elseif D1 == 3 then

        ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 1
        ZNFX = 0
        ZCZC = 1
        ZDJX = 1
        qjs = 1
        ZDSL = 1
        YJZL = 0
        ZDSP = 0
		chkg = 0
		zlkg=0
		CJ134=1
		dw()
		qjsshow()
        print((D1 .. "档 --混伤惩戒"))
    elseif D1 == 4 then

        ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 1
        ZNFX = 1
        ZCZC = 1
        ZDJX = 1
        qjs = 0
        ZDSL = 1
        YJZL = 0
        ZDSP = 0
		chkg = 1
		zlkg=0
		CJ134=0
		jzndj=0
		dw()
		qjsshow()
        print((D1 .. "档 --近战奶 高级模式 战地医生"))
    elseif D1 == 5 then

        ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 1
        ZNFX = 1
        ZCZC = 0
        ZDJX = 1
        qjs = 1
        ZDSL = 0
        YJZL = 1
        ZDSP = 2
		chkg = 0
		zlkg=0
		CJ134=1
		dw()
		qjsshow()
        print((D1 .. "档 --物理惩戒"))
    elseif D1 == 0 then

        ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 1
        ZNFX = 1
        ZCZC = 1
        ZDJX = 1
        qjs = 0
        ZDSL = 1
        YJZL = 0
        ZDSP = 1
		chkg =1
		zlkg=0
		CJ134=1
		dw()
		qjsshow()
        print((D1 .. "档 --传统奶"))
	elseif D1 == 6 then
		ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 1
        ZNFX = 0
        ZCZC = 0
        ZDJX = 1
        qjs = 1
        ZDSL = 0
        YJZL = 1
        ZDSP = 0
		chkg = 0
		zlkg=0
		CJ134=1
		dw()
		qjsshow()
        print((D1 .. "档 --毛伤害模式"))
	elseif D1 == -1 then
        ZB = 0
        LYZN = 1
        ZDWD = 0
        ZDCY = 0
        ZDZF = 1
        ZNFX = 0
        ZCZC = 1
        ZDJX = 1
        qjs = 1
        ZDSL = 1
        YJZL = 0
        ZDSP = 0
		chkg = 0
		zlkg=0
		CJ134=2
		dw()
		qjsshow()
        print(("初期练级模式,建议30级之后单刷防骑模式更快"))
    end
end


--mosButton:SetScript("OnClick", function()
 --   if not D1 then
 --       D1 = 0
  --  end
  --  if D1 < 5 then
 --       D1 = D1 + 1
 --   else
  --      D1 = 0
  --  end
 --   ApplyMode()
--	key()
--end)



wi=19
local modeButton0 = CreateFrame("Button", "ModeButton0", UIParent, "GameMenuButtonTemplate")
modeButton0:SetWidth(wi)  
modeButton0:SetHeight(20) 
modeButton0:SetText("奶")  
modeButton0:SetPoint("CENTER", mosButton, "RIGHT", -110, -60)  
modeButton0:SetScript("OnClick", function()
chButton0:Hide()
dqButton0:Hide()
zlButton0:Hide()
jymsButton0:Hide()
    D1 = 0  
    ApplyMode()  
 key()
end)
local zlButton0 = CreateFrame("Button", "zlButton0", UIParent, "GameMenuButtonTemplate")
zlButton0:SetWidth(50)  
zlButton0:SetHeight(35) 
zlButton0:SetText("奶")  
zlButton0:SetPoint("LEFT", modeButton0, "left", -60, -27)  
zlButton0:Hide()

zlButton0:SetScript("OnClick", function()

if not zlkg or zlkg ==1 then  zlkg = 0  print("主动治疗  开") zlButton0:SetText("奶") 
elseif zlkg == 0 then zlkg=1 print("主动治疗  关 --孢子男模式") zlButton0:SetText("不奶") 
end 
end)
--------------------------------------------------
--if UnitLevel("player") <=40 then
local expButton30 = CreateFrame("Button", "expButton0", UIParent, "GameMenuButtonTemplate")
expButton30:SetWidth(wi)  
expButton30:SetHeight(20) 
expButton30:SetText("练")  
expButton30:SetPoint("CENTER", mosButton, "RIGHT", 10, -60)  
--expButton30:Hide()
expButton30:SetScript("OnClick", function()
chButton0:Hide()
dqButton0:Hide()
zlButton0:Hide()
jymsButton0:Hide()
    D1 = -1 
	 key()
    ApplyMode()  

end)
--end




------------------------------------------------
local chButton0 = CreateFrame("Button", "chButton0", UIParent, "GameMenuButtonTemplate")
chButton0:SetWidth(50)  
chButton0:SetHeight(35) 
chButton0:SetText("忏悔")  
chButton0:SetPoint("LEFT", modeButton0, "left", -60, -27)  
chButton0:Hide()
chButton0:SetScript("OnClick", function()
if not chkg or chkg ==1 then  chkg = 0  print("自动忏悔  开") chButton0:SetText("忏悔") 
elseif chkg == 0 then chkg=1 print("自动忏悔  关") chButton0:SetText("不忏悔") 
end 
end)




local dqButton0 = CreateFrame("Button", "dqButton0", UIParent, "GameMenuButtonTemplate")
dqButton0:SetWidth(50) 
dqButton0:SetHeight(35)
dqButton0:SetText("壁垒") 
dqButton0:SetPoint("LEFT", modeButton0, "left", -60, -27) 
dqButton0:Hide()
dqButton0:SetScript("OnClick", function()
if SpellReady("正义壁垒")  and IsActionInRange(12)==1 and not UnitIsFriend("player","target") then
CastSpellByName("正义壁垒")
Print("盾墙开启.")
end
end)








local maoButton0 = CreateFrame("Button", "maoButton0", UIParent, "GameMenuButtonTemplate")
maoButton0:SetWidth(30) 
maoButton0:SetHeight(20) 
maoButton0:SetText("AOE") 
maoButton0:SetPoint("left", button, "RIGHT",50, -200) 
maoButton0:Hide()
maoButton0:SetScript("OnClick", function()
jymsButton0:Hide()
chButton0:Show()
dqButton0:Hide()
zlButton0:Hide()
    D1 = 6 
	ApplyMode()
	key()
end)



local modeButton1 = CreateFrame("Button", "ModeButton1", UIParent, "GameMenuButtonTemplate")
modeButton1:SetWidth(wi)  
modeButton1:SetHeight(20) 
modeButton1:SetText("坦")  
modeButton1:SetPoint("LEFT", modeButton0, "RIGHT", 1, 0)  
modeButton1:SetScript("OnClick", function()
jymsButton0:Hide()
chButton0:Hide()
dqButton0:Show()
zlButton0:Hide()
    D1 = 1 
    ApplyMode() 
	key()

end)


local modeButton2 = CreateFrame("Button", "ModeButton2", UIParent, "GameMenuButtonTemplate")
modeButton2:SetWidth(wi) 
modeButton2:SetHeight(20) 
modeButton2:SetText("刷") 
modeButton2:SetPoint("LEFT", modeButton1, "RIGHT", 1, 0) 
modeButton2:SetScript("OnClick", function()
jymsButton0:Hide()
chButton0:Hide()
dqButton0:Show()
zlButton0:Hide()
    D1 = 2 
    ApplyMode() 
key()
end)

local modeButton3 = CreateFrame("Button", "ModeButton3", UIParent, "GameMenuButtonTemplate")
modeButton3:SetWidth(wi)  
modeButton3:SetHeight(20) 
modeButton3:SetText("混") 
modeButton3:SetPoint("LEFT", modeButton2, "RIGHT", 1, 0)  
modeButton3:SetScript("OnClick", function()
jymsButton0:Show()
chButton0:Show()
dqButton0:Hide()
zlButton0:Hide()
    D1 = 3 
    ApplyMode()  
key()
	chkgoi()
	jyms=0
if jyms==1 then
print("-- Semi-Auto 手动开第一个圣印 用于buff极多战斗")  jymsButton0:SetText("S.A") end
if jyms==0 then
print("-- Auto 全自动输出循环. ") jymsButton0:SetText("Auto")   end
end)


local modeButton4 = CreateFrame("Button", "ModeButton4", UIParent, "GameMenuButtonTemplate")
modeButton4:SetWidth(wi)  
modeButton4:SetHeight(20) 
modeButton4:SetText("医")  
modeButton4:SetPoint("LEFT", modeButton3, "RIGHT", 1, 0)  
modeButton4:SetScript("OnClick", function()
jymsButton0:Hide()
chButton0:Hide()
dqButton0:Hide()
zlButton0:Show()
    D1 = 4  
    ApplyMode() 
key()
end)

local modeButton5 = CreateFrame("Button", "ModeButton5", UIParent, "GameMenuButtonTemplate")
modeButton5:SetWidth(wi)  
modeButton5:SetHeight(20) 
modeButton5:SetText("物") 
modeButton5:SetPoint("LEFT", modeButton4, "RIGHT", 1, 0)  
modeButton5:SetScript("OnClick", function()
jymsButton0:Hide()
chButton0:Show()
dqButton0:Hide()
zlButton0:Hide()
    D1 = 5 
    ApplyMode() 
key()
	chkgoi()
end)

local jymsButton0 = CreateFrame("Button", "jymsButton0", UIParent, "UIPanelButtonTemplate")
jymsButton0:SetWidth(40)  
jymsButton0:SetHeight(20) 
jymsButton0:SetText("-")  
jymsButton0:SetPoint("LEFT", modeButton0, "RIGHT", -70, 0)  
jymsButton0:Hide()
jymsButton0:SetScript("OnClick", function()

if jyms==0 then jyms=1  
else jyms=0  
end 

if jyms==1 then
print("-- Semi-Auto 手动开第一个圣印 用于buff极多战斗")  jymsButton0:SetText("S.A") end

if jyms==0 then
print("-- Auto 全自动输出循环. ") jymsButton0:SetText("Auto")   end

buffi=0 for buffj=1,40 do if UnitBuff("target",buffj) then buffi=buffi+1 end end 
Print("当前目标debuff总数："..buffi.."/32") 
end)
-----------------------------------------------------------

----------------------------------------------------------
modeButton6 = CreateFrame("Button", "ModeButton6", UIParent, "UIPanelButtonTemplate")
modeButton6:SetWidth(39)  
modeButton6:SetHeight(30) 
modeButton6:SetText("自驱")  
modeButton6:SetPoint("CENTER", mosButton, "RIGHT", -100, -90)  

modeButton6:SetScript("OnClick", function()
    if not qjs then
        qjs = 0
    end
    if qjs < 2 then
        qjs = qjs + 1
    else
        qjs = 0
    end

    if qjs == 0 then
        print("自我清洁术模式")
		qjsButton:SetText("自我驱散 ●")
		modeButton6:SetText("自驱")  
    elseif qjs == 1 then
        print("关闭清洁术")
		qjsButton:SetText("不驱散 ●")
		modeButton6:SetText("不驱")  
    elseif qjs == 2 then
        print("一键驱除功能开启,跑跳中 优先驱散")
		qjsButton:SetText("团队驱散 ●")
		modeButton6:SetText("团驱")  
    end
end)


local wzButton6 = CreateFrame("Button", "wzButton6", UIParent, "UIPanelButtonTemplate")
wzButton6:SetWidth(39)  
wzButton6:SetHeight(30) 
wzButton6:SetText("卡怪")  
wzButton6:SetPoint("LEFT", modeButton6, "RIGHT", 1, 0)  
wzButton6:SetScript("OnClick", function()
			TargetUnit("player")
			CastSpellByName("强效王者祝福")
			CastSpellByName("强效力量祝福")
			TargetLastTarget()
end)


local spButton6 = CreateFrame("Button", "wzButton6", UIParent, "UIPanelButtonTemplate")
spButton6:SetWidth(39) 
spButton6:SetHeight(30) 
spButton6:SetText("饰品")  
spButton6:SetPoint("LEFT", wzButton6, "RIGHT", 1, 0)  


spButton6:SetScript("OnClick", function()
TryUseTrinkets()
end)
-------------------------------------------
ysButton3 = CreateFrame("Button", "ysButton3", UIParent, "UIPanelButtonTemplate")
ysButton3:SetWidth(12) 
ysButton3:SetHeight(12) 
ysButton3:SetText("")  
ysButton3:SetPoint("BOTTOMLEFT", spButton6, "RIGHT", 1, 2)  

ysButton3:SetScript("OnClick", function()

if fnzcoi==0 then fnzcoi=1 print("• 自动 愤怒之锤 关闭.") ysButton3:SetText("")  
else fnzcoi=0   print("•• 自动 愤怒之锤 开启.") ysButton3:SetText("•")   end
end)

ysButton4 = CreateFrame("Button", "ysButton4", UIParent, "UIPanelButtonTemplate")
ysButton4:SetWidth(12) 
ysButton4:SetHeight(12) 
ysButton4:SetText("")  
ysButton4:SetPoint("LEFT", ysButton3, "RIGHT", 1, 0)  


ysButton4:SetScript("OnClick", function()
if zdkspoi==0 then zdkspoi=1 print("• 自动饰品 关闭.") ysButton4:SetText("")  
else zdkspoi=0   print("•• 自动饰品 开启.") ysButton4:SetText("•")   end


end)

qxButton6 = CreateFrame("Button", "qxButton6", UIParent, "UIPanelButtonTemplate")
qxButton6:SetWidth(12) 
qxButton6:SetHeight(12) 
qxButton6:SetText("•")  
qxButton6:SetPoint("TOPLEFT", spButton6, "RIGHT", 1, 0)  


qxButton6:SetScript("OnClick", function()
if qxsoi==0 then qxsoi=1 print("• 驱邪术 关闭.") qxButton6:SetText("")  
else qxsoi=0   print("•• 驱邪术 开启.") qxButton6:SetText("•")  end
end)

zcButton6 = CreateFrame("Button", "zcButton6", UIParent, "UIPanelButtonTemplate")
zcButton6:SetWidth(12) 
zcButton6:SetHeight(12) 
zcButton6:SetText("")  
zcButton6:SetPoint("LEFT", qxButton6, "RIGHT", 1, 0)  

zcButton6:SetScript("OnClick", function()
if zczcoi==0 then zczcoi=1 print("• 自动制裁 关闭.") zcButton6:SetText("")  
else zczcoi=0   print("•• 自动制裁 开启.") zcButton6:SetText("•")   end
end)


swButton6 = CreateFrame("Button", "swButton6", UIParent, "UIPanelButtonTemplate")
swButton6:SetWidth(35) 
swButton6:SetHeight(20) 
swButton6:SetText("圣物")  
swButton6:SetPoint("LEFT", modeButton6, "RIGHT", 85, -30) 

swButton6:SetScript("OnClick", function()
if autoswoi  ==0 then autoswoi=1 print("--自动更换圣物 关闭.") swButton6:SetText("不换")  
else autoswoi=0   print("自动更换圣物 开.") swButton6:SetText("换")   end
end)

----------------------------------------------------
qcButton6 = CreateFrame("Button", "qcButton6", UIParent, "UIPanelButtonTemplate")
qcButton6:SetWidth(35) 
qcButton6:SetHeight(20) 
qcButton6:SetText("群嘲")  
qcButton6:SetPoint("LEFT", modeButton6, "RIGHT", 85, -50) 

qcButton6:SetScript("OnClick", function()
Print("准备 '活动假人' 在背包里.")
Print("制作宏 /run zmqcbh()")
Print("需要用到superwow,鼠标指向位置 狂点即可.")
end)


---------------------------------------------------

local ssButton6 = CreateFrame("Button", "ssButton6", UIParent, "UIPanelButtonTemplate")
ssButton6:SetWidth(70)  
ssButton6:SetHeight(20) 
ssButton6:SetText("圣水 关")  
ssButton6:SetPoint("LEFT", modeButton6, "RIGHT", -39, -30) 


ssButton6:SetScript("OnClick", function()
  if stss == nil or stss == 1 then
        stss = 0
        print("自动使用斯坦索姆圣水.需要用到superwow")

		ssButton6:SetText("圣水 开")
    else
        stss = 1
        print("--不使用斯坦索姆圣水.")
		ssButton6:SetText("圣水 关")
    end
end)
------------------------------------------------------------

local zyzsButton6 = CreateFrame("Button", "zyzsButton6", UIParent, "UIPanelButtonTemplate")
zyzsButton6:SetWidth(70)  
zyzsButton6:SetHeight(20) 
zyzsButton6:SetText("自由之手")  
zyzsButton6:SetPoint("LEFT", modeButton6, "RIGHT", -39, -50) 


zyzsButton6:SetScript("OnClick", function()
 if zyzs==0 then zyzs=1 zyzsButton6:SetText("自由 关")    print("--关闭自动 自由之手.")
 else zyzs=0 zyzsButton6:SetText("自由 开")    print("自动使用 自由之手.")
 end
end)


-------------------------------------------------------------------
local kfourButton6 = CreateFrame("Button", "kfourButton6", UIParent, "UIPanelButtonTemplate")
kfourButton6:SetWidth(55)  
kfourButton6:SetHeight(20) 
kfourButton6:SetText("K40")  
kfourButton6:SetPoint("LEFT", ssButton6, "RIGHT", 0, -20) 


kfourButton6:SetScript("OnClick", function()
if kfour==0 then kfour=1 kfourButton6:SetText("K40关")    print("--关闭K40自动驱散")
else kfour=0 kfourButton6:SetText("K40开")   print("K40副本自动驱散: 龙人连线,象棋下跪,尾王眼睛点名") 
end
end)
---------------------------------------------------------------


local yuliuButton6 = CreateFrame("Button", "yuliuButton6", UIParent, "UIPanelButtonTemplate")
yuliuButton6:SetWidth(55)  
yuliuButton6:SetHeight(20) 
yuliuButton6:SetText("自保")  
yuliuButton6:SetPoint("LEFT", ssButton6, "RIGHT", 0, 0)  


yuliuButton6:SetScript("OnClick", function()
	if ZDWD==0 then ZDWD=1 yuliuButton6:SetText("自保关") print("--关闭 自动 圣盾术 or 保护祝福")
	else ZDWD=0 yuliuButton6:SetText("自保开")  print("开启自保功能")
	end
end)


function usesw(swName)
if autoswoi==0 then
UseItemByName(swName)
end
end

function qjsshow()
  if qjs == 0 then

		qjsButton:SetText("自我驱散 ●")
		modeButton6:SetText("自驱")  
    elseif qjs == 1 then

		qjsButton:SetText("不驱散 ●")
		modeButton6:SetText("不驱")  
    elseif qjs == 2 then

		qjsButton:SetText("团队驱散 ●")
		modeButton6:SetText("团驱")  
    end
end

function key()

	modeButton0:SetTextColor(1, 1, 1)
    modeButton1:SetTextColor(1, 1, 1)
    modeButton2:SetTextColor(1, 1, 1)
    modeButton3:SetTextColor(1, 1, 1)
    modeButton4:SetTextColor(1, 1, 1)
    modeButton5:SetTextColor(1, 1, 1)
	maoButton0:SetTextColor(1, 1, 1)
	expButton30:SetTextColor(1, 1, 1)

    if D1 == 1 then
        modeButton1:SetTextColor(0, 1, 0)
    elseif D1 == 2 then
        modeButton2:SetTextColor(0, 1, 0)
    elseif D1 == 3 then
        modeButton3:SetTextColor(0, 1, 0)
    elseif D1 == 4 then
        modeButton4:SetTextColor(0, 1, 0)
    elseif D1 == 5 then
        modeButton5:SetTextColor(0, 1, 0)
	elseif D1 == 0 then
        modeButton0:SetTextColor(0, 1, 0)	
	elseif D1 == 6 then
        maoButton0:SetTextColor(0, 1, 0)			
	elseif D1 == -1 then
        expButton30:SetTextColor(0, 1, 0)			
    end
end

function xymr()
    ZAOMAODB.x = 0
    ZAOMAODB.y = 0
    button:ClearAllPoints()  -- 清除所有现有锚点
    button:SetPoint("TOPLEFT", UIParent, "TOPLEFT", 0, 0)  -- 重新设置到左上角
end

function zmqcbh()
if SpellReady("保护之手") and UnitAffectingCombat("player")  then
	UseItemByName("活动假人")
	CameraOrSelectOrMoveStart()
	CameraOrSelectOrMoveStop()
	local t,c="活动假人","保护之手" TargetByName(t) 
	if GetUnitName("target") == t then 
	CastSpellByName(c)   TargetLastTarget() 
	end
else
print("非战斗中 或 保护之手 技能cd. ")
end
end


-----------------------------------------------------------------------------


-------------------------------------------------------------------------------

function miji()
maoButton0:Show()
end

SLASH_ZMPALADIN1 = "/zmpal"
SlashCmdList["ZMPALADIN"] = ExecuteScript

SLASH_MIJI1 = "/whosyourdaddy"
SlashCmdList["MIJI"] = function()
    miji()
end

end


