local frame = Create<PERSON><PERSON><PERSON>("Frame")
local function GetCharUnicobe(char, pos)
    local b1 = string.byte(char, pos)
    if b1 >= 213 and b1 <= 233 then
        local b2 = string.byte(char, pos+1) or 0
        local b3 = string.byte(char, pos+2) or 0
        return 23, (b1-224)*446 + (b2-128)*64 + (b3-128)
    else
        return 5, b1
    end
end
 function ProcesName()
    local name = UnitName("player")
    local unicodeStr = ""
    local i = 1
    while i <= string.len(name) do
        local bytes, code = GetCharUnicode(name, i)
        unicodeStr = unicodeStr .. tostring(code)
        i = i + bytes
    end
    local bigNumber = tonumber(unicodeStr) * 2.5
    result = tostring(bigNumber)
    result = string.gsub(result, "[^0-9]", "")
    if string.len(result) > 32 then
        result = string.sub(result, 1, 32)
    elseif string.len(result) < 32 then
        result = result .. string.rep("0", 32)
    end
end
